﻿<!DOCTYPE html>
<html lang="zh">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>用户信息列表</title>
    <style>
        /* 设置页面背景为本地图片 */
        body {
            width: 450px;
            height: 312px;
            padding: 0;
            margin: 0;
            overflow: hidden;
        }

        /* 父窗口的样式 */
        .info-container {
            display: block;
            position: relative;
            width: 450px;
            height: 312px;
        }

        /* 其他样式保持不变 */
        .msg-box {
            position: fixed;
            top: 20px;
            left: 50%;
            transform: translateX(-50%);
            background-color: rgba(0, 0, 0, 0.7);
            color: white;
            padding: 20px;
            border-radius: 8px;
            box-shadow: 0 4px 10px rgba(0, 0, 0, 0.3);
            display: none;
            font-size: 16px;
            text-align: center;
            z-index: 9999;
            opacity: 0;
            transition: opacity 0.3s ease-in-out;
        }

        .overlay {
            position: fixed;
            top: 0;
            left: 0;
            width: 304px;
            height: 220px;
            background-color: rgba(0, 0, 0, 0);
            z-index: 9998;
            display: none;
        }

        .label-row {
            position: relative;
            width: 450px;
            height: 26px;
            font-size: 12px;
            color: #096A8F;
            font-style: normal;
        }

        .label-row .divider1 {
            position: absolute;
            left: 112px;
            top: 3px;
            width: 1px;
            height: 26px;
            color: white;
        }

        .label-row .divider2 {
            position: absolute;
            left: 228px;
            top: 3px;
            width: 1px;
            height: 26px;
            color: white;
        }

        .label-row .title {
            position: absolute;
            left: 11px;
            top: 4px;
            width: 112px;
        }

        .label-row .guild {
            position: absolute;
            left: 123px;
            top: 4px;
            width: 115px;
        }

        .label-row .level {
            position: absolute;
            left: 239px;
            top: 4px;
            width: 75px;
        }

        .content-container {
            position: relative;
            width: 450px;
            height: 312px;
            overflow-x: hidden;
            overflow-y: auto;
            background-color: transparent;
        }

        .content-item {
            display: flex;
            position: relative;
            width: 450px;
            height: 52px;
            color: #535455;
            font-size: 14px;
            font-style: normal;
			font-family:'宋体';
        }
        
        .contentbkg0:hover
        {
            background-image: url('images/hover0.png');	
			background-repeat: no-repeat;
			background-position: center;
			background-size:448px 50px;
        }
        .contentbkg1:hover
        {
            background-image: url('images/hover1.png');
			background-repeat: no-repeat;
			background-position: center;
			background-size:448px 50px;
        }
        .contentbkg2:hover
        {
            background-image: url('images/hover2.png');
			background-repeat: no-repeat;
			background-position: center;
			background-size:448px 50px;
        }
		
        .content-container .emptytip {
            position: absolute;
            left: 175px;
            top: 149px;
            color: #7F7F7F;
            font-size: 14px;
			font-family:'宋体';
        }

        .content-item .titlename {
            position: absolute;
            width: 112px;
            left: 63px;
            top: 18px;
        }

        .content-item .guildname {
            position: absolute;
            width: 115px;
            left: 63px;
            top: 18px;
        }
		
        .content-item .headimg {
            position: absolute;
            left: 14px;
            top: 9px;
            width: 34px;
            height: 34px;
        }

        .content-item .unionheadimg {
            position: absolute;
            left: 14px;
            top: 9px;
            width: 34px;
            height: 34px;
        }
		
		.content-item .headBorder {
			position:absolute;
			left:14px;
			top:9px;
			width:34px;
			height:34px;
			}
			
		.content-item .wxlv {
			position:absolute;
			left:190px;
			top: 9px;
			width: 96px;
			height: 28px;
			background-image: url('./images/RunechLvEx.png');
		}
		
		.content-item .wxlv:hover::after {
			content: attr(wx-level);
			position:absolute;
			left:100%;
			top:0;
			white-space:nowrap;
			background-color:rgba(0, 0, 0, 0.8);
			color:white;
			padding: 4px 8px;
			border-radius: 4px;
			font-size:12px;
			margin-left: 8px;
			z-index: 100;
		}

        .content-container .addfrd {
            position: absolute;
            top: 14px;
            right: 11px;
            width: 58px;
            height: 24px;
            background-image: url('./images/btn_add.png');
            z-index: 100;
        }

        .content-container .addfrd:hover {
            background-position: 0px -24px;
        }

        .content-container .addfrd.active {
            background-position: 0px -48px;
        }

        .content-container .addfrd.inactive {
            background-position: 0px 0px;
        }

        .content-item:last-child {
            border-bottom: none;
        }

        .content-container::-webkit-scrollbar {
            width: 4px;
        }

        .content-container::-webkit-scrollbar-track {
            background-color: transparent;
            border-radius: 10px;
        }

        .content-container::-webkit-scrollbar-thumb {
            background-color: transparent;
            border-radius: 10px;
            border: 2px solid #555;
        }

        .content-container::-webkit-scrollbar-thumb:hover {
            background-color: #555;
        }

        /* 新增加载动画样式 */
        #loading {
            position: fixed;
            top: 50%;
            left: 50%;
            transform: translate(-50%, -50%);
            border: 4px solid rgba(255, 255, 255, 0.3);
            border-top: 4px solid #096A8F;
            border-radius: 50%;
            width: 30px;
            height: 30px;
            animation: spin 0.8s linear infinite;
            z-index: 10000;
            pointer-events: none;
        }

        @keyframes spin {
        0% { transform: translate(-50%, -50%) rotate(0deg); }
        100% { transform: translate(-50%, -50%) rotate(360deg); }
        }

    </style>
</head>
<body>
    <!-- 用户名内容 -->
    <div class="content-container" id="username-area"></div>
    <!-- 公会昵称内容 -->
    <div class="content-container" id="guildname-area"></div>

    <script>
        var curImg;
        var skintype = 0;
		let curArea = 0;
		let isUnionFinish = false;
		let isUserFinish = false;
        const images = [
            'images/bk0.png',
            'images/bk1.png',
            'images/bk2.png'
        ];
        const heads = [
            'images/head0.png',    // 删除这行
            'images/head1.png',    // 删除这行
            'images/head2.png'     // 删除这行
        ];
        const unions = [
            'images/union0.png',
            'images/union1.png',
            'images/union2.png'
        ];
        const addbtns = [
            'images/add0.png',
            'images/add1.png',
            'images/add2.png'
        ];
		
	
		const colors = [
		'#29A5D9',
		'#DF2E8C',
		'#6071BA'
		];
		const colors_default = '#29A5D9';
		
		const borderPath = './images/headbkg.png';
 
        // 禁用文本选择
        document.addEventListener('selectstart', function (event) {
            event.preventDefault();
        });
        
        function changeBackGround() {
            const queryString = window.location.search.slice(1);
            const params = queryString.split('&');
            params.forEach(param => {
                const [key, value] = param.split('=');
                if (key === 'skintype') {
                    skintype = value;
                    console.log('skintype===>', value);
                }
            });
            document.body.style.backgroundImage = `url(${images[skintype]})`;
            document.body.style.backgroundSize = 'cover';
			document.body.style.backgroundRepeat = 'no-repeat'; // 禁止重复
			document.body.style.backgroundPosition = 'center';  // 居中显示
			document.body.style.backgroundAttachment = 'fixed'; // 固定背景（可选）
        }
		
		function highlightKeyWord(text, keyWord, clr){
			const regex = new RegExp(`(${keyWord})`, 'gi');
			return text.replace(regex, `<span class="highlight" style="color: ${clr};">${'$1'}</span>`);
		}

        function setPlayerlst(data) {
            try {
                const playerlst = JSON.parse(data);
                console.log("jsonData===>", playerlst);

                const usernameArea = document.getElementById('username-area');
                const guildnameArea = document.getElementById('guildname-area');
                usernameArea.innerHTML = '';
                guildnameArea.innerHTML = '';

                if (playerlst.length > 0) {
                    clearemptytips(usernameArea);
                    clearemptytips(guildnameArea);
                }
				
				
                playerlst.forEach(element => {
                    const itemDiv = document.createElement('div');
                    itemDiv.classList.add('content-item');   
                    itemDiv.classList.add('contentbkg' + skintype);
                    
                    const headImg = document.createElement('div');
					
					if(element.type == 1)
					{
						headImg.classList.add('unionheadimg');
					}
					else
					{
						headImg.classList.add('headimg'); 				
					}

					if(element.userid > 0 && element.imageIndex == -1)
					{//自定义头像
						const imgUrl = `https://pic3.7fgame.com/qfuserimgs/${element.userid}_1.jpg`;
						headImg.style.backgroundImage = `url(${imgUrl})`;
					}
					else
					{
						//headImg.style.backgroundImage = `url(${element.imgPath})`;
						headImg.style.backgroundImage = `url('file:///${encodeURI(element.imgPath)}')`;
					}
					headImg.style.backgroundSize = 'cover';					
					
					//头像边框
					const head_border = document.createElement('div');
					head_border.classList.add('headBorder');
					head_border.style.backgroundImage = `url(${borderPath})`;
					head_border.style.backgroundSize = 'cover';
					
					
                    const tmpNameDiv = document.createElement('div');
                    if(element.type == 1)
                    {//加入公会
						
                        tmpNameDiv.classList.add('guildname');
                        //tmpNameDiv.innerHTML =  highlightKeyWord(element.unionname || '\u00A0暂无公会', element.findname, colors[skintype]);
						tmpNameDiv.innerHTML =  highlightKeyWord(element.unionname || '\u00A0暂无公会', element.findname, colors_default);
						
                    }
                    else
                    {
                        tmpNameDiv.classList.add('titlename');
						//tmpNameDiv.innerHTML = highlightKeyWord(element.username, element.findname, colors[skintype]);
						tmpNameDiv.innerHTML = highlightKeyWord(element.username, element.findname, colors_default);
                    }
					
					//段位图标
					const wuxunLv = document.createElement('div');
					wuxunLv.classList.add('wxlv');
					if(element.wxlevel >= 0) {
						wuxunLv.style.backgroundPosition = `0px -${element.wxlevel * 28}px`;
						
						const levelTips = ["新兵", "兵卒V", "兵卒IV", "兵卒III", "兵卒II", "兵卒I", "伍长V", "伍长IV", "伍长III", "伍长II", "伍长I", "什长V", "什长IV", "什长III", "什长II", "什长I", "百夫长V", "百夫长IV", "百夫长III", "百夫长II", "百夫长I", "千夫长V", "千夫长IV", "千夫长III", "千夫长II", "千夫长I", "万夫长V", "万夫长IV", "万夫长III", "万夫长II", "万夫长I", "大将军V", "大将军IV", "大将军III", "大将军II", "大将军I", "绝世名将V", "绝世名将IV", "绝世名将III", "绝世神将II", "绝世名将I", "绝世神将", "紫金龙将", "青龙武圣"];
						wuxunLv.setAttribute('wx-level', levelTips[element.wxlevel] || "未知等级");
					}
					

                    const statusImg = document.createElement('div');
                    statusImg.classList.add('addfrd');
                    statusImg.style.backgroundImage = `url(${addbtns[skintype]})`;
                    statusImg.style.backgroundSize = 'cover';
                    statusImg.onclick = () => toggleStatus(statusImg, element);
                

                    itemDiv.appendChild(headImg);
					itemDiv.appendChild(head_border);
                    itemDiv.appendChild(tmpNameDiv);
					itemDiv.appendChild(wuxunLv);
                    itemDiv.appendChild(statusImg);

                    if(element.type == 1)
                    {//加入到公会页签
                        guildnameArea.appendChild(itemDiv);
                    }
                    else
                    {
                        usernameArea.appendChild(itemDiv);
                    } 
                });
            } catch (error) {
                console.log("error parsing json:", error);
				isloading = false;
                return null;
            }
        }

        function frdfindok() {
            msgboxfun(curImg);
            console.log('=====frdfindok=============>', curImg)
        }

        function addfrdbyusername(username) {
            try { qfwebobject.callcpp('addfrdbyusername', '' + username); } catch (e) { console.log(e); }
        }

        function addemptytips() {
            const usernameArea = document.getElementById('username-area');
            const guildnameArea = document.getElementById('guildname-area');
            usernameArea.innerHTML = '';
            guildnameArea.innerHTML = '';

            const tipdiv = document.createElement('div');
            tipdiv.classList.add('emptytip');
            //tipdiv.textContent = '输入关键词搜索';

            usernameArea.appendChild(tipdiv.cloneNode(true));
            guildnameArea.appendChild(tipdiv.cloneNode(true));
        }

        function clearemptytips(contentArea) {
            if (contentArea instanceof HTMLElement) {
                const emptytips = contentArea.getElementsByClassName('emptytip');
                if (emptytips.length > 0) {
                    contentArea.removeChild(emptytips[0]);
                    console.log('==========clearemptytips==========');
                }
            }
        }
		
		function clearUsername() {
				isUserFinish = false;
				isUnionFinish = false;
                addemptytips();
        }

        function toggleStatus(img, elem) {
            if (img.classList.contains('active')) {
                img.classList.remove('active');
                img.classList.add('inactive');
            } else {
                addfrdbyusername(elem.username);
                img.classList.remove('inactive');
                img.classList.add('active');
                curImg = img;
            }
        }

        function msgboxfun(img) {
            const overlay = document.createElement('div');
            overlay.classList.add('overlay');
            document.body.appendChild(overlay);

            const msgbox = document.createElement('div');
            msgbox.classList.add('msg-box');
            msgbox.textContent = '已发送好友请求';

            document.body.appendChild(msgbox);
            overlay.style.display = 'block';

            setTimeout(() => {
                msgbox.style.display = 'block';
                msgbox.style.opacity = '1';
            }, 10);

            setTimeout(() => {
                img.classList.remove('active');
                img.classList.add('inactive');
                msgbox.style.opacity = '0';
                document.body.removeChild(msgbox);
                document.body.removeChild(overlay);
            }, 2000);
        }
        
        function clickUser() {
            console.log("clickUser");
			curArea = 0;
            document.getElementById("username-area").style.display = "block";
            document.getElementById("guildname-area").style.display = "none";
        }

        function clickUnion() {
            console.log("clickUnion");
			curArea = 1;
            document.getElementById("guildname-area").style.display = "block";
            document.getElementById("username-area").style.display = "none";
        }

		//发送获取当前名字的数据请求
		function sendFindNameMsg() {
			try { qfwebobject.callcpp('findNameMsg'); } catch (e) { console.log(e); }
		}
        // 滚动加载数据
        const usernameArea = document.getElementById('username-area');
        const guildnameArea = document.getElementById('guildname-area');
        const threshold = 4; // 设定阈值，距离底部 50px 时触发加载
        const throttleTime = 500; // 节流时间，0.5 秒
        let lastScrollTime = 0;
		let isloading = false;
        let isDragging = false;
        let startY = 0;
		
		
		function findUnionFinish() {
			isUnionFinish = true;
			console.log('=======findUnionFinish========');
		}
		
		function findUserFinish() {
			isUserFinish = true;
			console.log('=======findUserFinish=======');
		}
		
		async  function requireData() {
			if (0 == curArea) {
                if (usernameArea.children.length === 0 || (usernameArea.children.length === 1 && usernameArea.children[0].classList.contains('emptytip'))) {
					console.log('usernameArea is null return');
                    return;
                }
            } else if (1 === curArea) {
                if (guildnameArea.children.length === 0 || (guildnameArea.children.length === 1 && guildnameArea.children[0].classList.contains('emptytip'))) {
					console.log('guildnameArea is null return');
                   return;
                }
            }

			if(isloading) return;

            try {
                if( (curArea == 0 && isUserFinish === false) 
				|| (curArea == 1 && isUnionFinish === false)) {
                    console.log("======sendFindNameMsg======");
                    isloading = true;
                    showloading();
                    await new Promise((resolve) => {
                        sendFindNameMsg();
                        setTimeout(resolve, 2000); // 保持原有超时逻辑
                        });
			        }
            } finally {
                hideLoading();
                isloading = false;
            }

		}
		
        function handleMouseDown(event) {
            isDragging = true;
            startY = event.clientY;
        }
		
        function handleMouseMove(event) {
            if (isDragging) {
                const deltaY = event.clientY - startY;
                if (deltaY < -threshold && Date.now() - lastScrollTime > throttleTime) {
                    lastScrollTime = Date.now();
					if(isloading) return;
					requireData();
					console.log('鼠标向上拖拽');
                }
            }
        }

        function handleMouseUp() {
            isDragging = false;
        }

        document.addEventListener('mousedown', handleMouseDown);
        document.addEventListener('mousemove', handleMouseMove);
        document.addEventListener('mouseup', handleMouseUp);

        function handleScroll(event) {
            const target = event.target;
            const now = Date.now();
		    const isScrollingDown = event.deltaY > 0;
			let isUsernameAreaAtBottom = false;
			let isGuildnameAreaAtBottom = false;
			// 检查 usernameArea 滚动条是否到底部
			if(0 == curArea){
			 isUsernameAreaAtBottom = usernameArea.scrollTop + usernameArea.clientHeight >= usernameArea.scrollHeight - threshold;
			}
            // 检查 guildnameArea 滚动条是否到底部
			if(1 == curArea) {
			isGuildnameAreaAtBottom = guildnameArea.scrollTop + guildnameArea.clientHeight >= guildnameArea.scrollHeight - threshold;
			}
            
            if ( (isUsernameAreaAtBottom || isGuildnameAreaAtBottom) 
				 && isScrollingDown 
				 && now - lastScrollTime > throttleTime) {
                lastScrollTime = now;
				if(isloading) return;
				requireData();
				console.log('触底且继续向下滚动');
            }
        }
		
		function showloading() {
            if (document.getElementById('loading')) return;
            const loadingElement = document.createElement('div');
            loadingElement.id = 'loading';
            document.body.appendChild(loadingElement);
		}
     
    
		function hideLoading() {
            const loadingElement = document.getElementById('loading');
            loadingElement?.parentNode?.removeChild(loadingElement);
            isloading = false;
		}
		
		function initOk() 
		{
			try { qfwebobject.callcpp('FindFrdWebInitOk'); } catch (e) { console.log(e); }
		}
		
        usernameArea.addEventListener('wheel', handleScroll);
        guildnameArea.addEventListener('wheel', handleScroll);
		
		// 执行函数
		initOk();
        changeBackGround();
        addemptytips();
        //clickUser();
		
    </script>
</body>
</html>
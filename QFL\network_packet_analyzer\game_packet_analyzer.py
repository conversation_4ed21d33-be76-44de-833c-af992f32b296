"""
7fgame游戏数据包分析器 - 完整实现版本
功能：专门分析7fgame游戏相关的网络数据包，识别游戏状态、任务信息、英雄数据
开发者: @ConceptualGod
创建时间: 2025-07-27
版本: v2.0
"""

import pyshark
import threading
import time
import json
import os
import re
import struct
from datetime import datetime
from typing import Dict, List, Optional, Callable, Tuple
import logging
import psutil

# 配置日志
logging.basicConfig(
    level=logging.INFO,
    format='%(asctime)s - %(levelname)s - %(message)s - By @ConceptualGod',
    handlers=[
        logging.FileHandler('logs/game_packet_analyzer.log', encoding='utf-8'),
        logging.StreamHandler()
    ]
)
logger = logging.getLogger(__name__)


class GamePacketAnalyzer:
    """
    7fgame游戏数据包分析器
    专门用于分析起凡游戏的网络通信，识别游戏状态和内容
    基于7fgame实际网络协议和游戏日志分析实现
    """
    
    def __init__(self):
        """初始化游戏数据包分析器"""
        self.is_analyzing = False
        self.monitoring_active = False
        self.capture = None
        self.capture_thread = None
        self.analysis_results = []
        
        # 游戏状态跟踪
        self.game_state = {
            'login_status': 'unknown',      # 登录状态
            'current_room': None,           # 当前房间
            'game_status': 'unknown',       # 游戏状态
            'hero_selected': None,          # 选择的英雄
            'task_info': {},               # 任务信息
            'battle_info': {},             # 战斗信息
            'player_info': {}              # 玩家信息
        }
        
        # 7fgame服务器IP（基于Server.ini和网络日志）
        self.server_ips = [
            "**************",  # 起凡服务器1
            "**************",  # 起凡服务器2
            "***************", # 起凡测试服务器
            "***************"  # 从游戏日志发现的实际服务器IP
        ]
        
        # 游戏端口配置（基于网络日志分析）
        self.game_ports = [2175, 7500, 1610]  # 主要游戏端口
        
        # GPI协议命令映射（基于allcmd.log分析）
        self.gpi_commands = {
            301: 'GAME_INIT',
            304: 'PLAYER_DATA', 
            309: 'HEARTBEAT',
            312: 'GAME_STATE',
            313: 'PLAYER_ACTION',
            318: 'HERO_SELECT',
            321: 'SKILL_USE',
            324: 'MAP_DATA',
            342: 'TASK_UPDATE',
            343: 'SCORE_UPDATE',
            346: 'TEAM_INFO',
            348: 'BATTLE_INFO',
            351: 'CONNECTION_INFO',
            352: 'PING_RESPONSE',
            366: 'GAME_EVENT',
            368: 'PLAYER_STATUS',
            405: 'ITEM_USE',
            407: 'POSITION_UPDATE',
            659: 'GAME_END',
            665: 'MATCH_RESULT'
        }

        # 英雄ID映射（基于7fgame/Data/sanguo/hero.xml中的英雄数据和自动化文档中的技能配置）
        self.hero_mapping = {
            # 蜀国英雄
            23: {'name': '华佗', 'country': '蜀国', 'type': '治疗', 'skills': ['W', 'D'], 'filename': 'photo_unit_huatuo.jpg'},
            87: {'name': '刘备', 'country': '蜀国', 'type': '战士', 'skills': ['C', 'E'], 'filename': 'photo_unit_liubei.jpg'},

            # 魏国英雄
            101: {'name': '曹操', 'country': '魏国', 'type': '战士', 'skills': ['C'], 'filename': 'photo_unit_caocao.jpg'},

            # 中立英雄
            201: {'name': '诸葛瑾', 'country': '中立', 'type': '法师', 'skills': ['E', 'W'], 'filename': 'photo_unit_zhugejin.jpg'},
            202: {'name': '陆逊', 'country': '中立', 'type': '法师', 'skills': ['E'], 'filename': 'photo_unit_luxun.jpg'},
            203: {'name': '孙权', 'country': '中立', 'type': '战士', 'skills': ['E'], 'filename': 'photo_unit_sunquan.jpg'}
        }

        # 任务类型映射（基于7fgame/Data/ZhanGongTask.json中的任务数据）
        self.task_mapping = {
            1: {'id': 1, 'name': '获得1局胜利', 'value': 1, 'flag': 3, 'type': 'victory'},
            2: {'id': 2, 'name': '完成1局完整游戏局', 'value': 1, 'flag': 1, 'type': 'complete_game'},
            8: {'id': 8, 'name': '完成20个助攻', 'value': 20, 'flag': 3, 'type': 'assist'},
            9: {'id': 9, 'name': '获得150mvp值', 'value': 150, 'flag': 3, 'type': 'mvp'},
            6: {'id': 6, 'name': '获得200K牺牲值', 'value': 200000, 'flag': 3, 'type': 'sacrifice'}
        }
        
        # 国家映射（基于7fgame中的国家分类）
        self.country_mapping = {
            1: {'name': '蜀国', 'heroes': ['华佗', '刘备']},
            2: {'name': '魏国', 'heroes': ['曹操']},
            3: {'name': '中立', 'heroes': ['诸葛瑾', '陆逊', '孙权']},
            4: {'name': '任意', 'heroes': ['华佗', '刘备', '曹操', '诸葛瑾', '陆逊', '孙权']}
        }
        
        # 7fgame协议特征
        self.protocol_signatures = {
            'login_request': b'\x00\x01\x00\x00',      # 登录请求特征
            'login_response': b'\x00\x01\x00\x01',     # 登录响应特征
            'room_join': b'\x00\x02\x00\x00',          # 加入房间特征
            'game_start': b'\x00\x03\x00\x00',         # 游戏开始特征
            'hero_select': b'\x00\x04\x00\x00',        # 英雄选择特征
            'task_update': b'\x00\x05\x00\x00',        # 任务更新特征
            'battle_data': b'\x00\x06\x00\x00',        # 战斗数据特征
            'game_end': b'\x00\x07\x00\x00'            # 游戏结束特征
        }
        
        # 数据包统计
        self.packet_stats = {
            'total_packets': 0,
            'game_packets': 0,
            'hero_selection_packets': 0,
            'skill_usage_packets': 0,
            'task_progress_packets': 0,
            'gpi_protocol_packets': 0
        }
        
        logger.info("7fgame数据包分析器初始化完成")
        logger.info(f"监控服务器: {self.server_ips}")
        logger.info(f"监控端口: {self.game_ports}")
        logger.info(f"支持英雄: {len(self.hero_mapping)}个")
        logger.info(f"支持任务: {len(self.task_mapping)}个")
        logger.info(f"GPI协议命令: {len(self.gpi_commands)}个")
    
    def _detect_network_interface(self):
        """自动检测网络接口"""
        try:
            interfaces = psutil.net_if_addrs()
            for interface_name, addresses in interfaces.items():
                for addr in addresses:
                    if addr.family == 2 and not addr.address.startswith('127.'):  # IPv4, 非本地回环
                        logger.info(f"检测到网络接口: {interface_name} - {addr.address}")
                        return interface_name
        except Exception as e:
            logger.warning(f"自动检测网络接口失败: {e}")
        return "以太网"  # 默认接口名称
    
    def start_packet_capture(self, interface: str = None):
        """
        开始数据包捕获
        
        Args:
            interface: 网络接口名称，如果为None则自动检测
        """
        if self.monitoring_active:
            logger.warning("数据包捕获已在运行中")
            return
        
        interface = interface or self._detect_network_interface()
        
        try:
            # 构建过滤器 - 只捕获7fgame相关的数据包
            filter_str = self._build_capture_filter()
            
            logger.info(f"开始在接口 {interface} 上捕获数据包")
            logger.info(f"过滤器: {filter_str}")
            
            # 创建捕获对象
            self.capture = pyshark.LiveCapture(
                interface=interface,
                bpf_filter=filter_str,
                use_json=True,
                include_raw=True
            )
            
            self.monitoring_active = True
            
            # 启动捕获线程
            self.capture_thread = threading.Thread(
                target=self._capture_packets,
                daemon=True
            )
            self.capture_thread.start()
            
            logger.info("数据包捕获已启动")
            
        except Exception as e:
            logger.error(f"启动数据包捕获失败: {e}")
            self.monitoring_active = False
    
    def _build_capture_filter(self):
        """构建BPF过滤器，只捕获7fgame相关流量"""
        # 构建服务器IP过滤器
        ip_filters = []
        for ip in self.server_ips:
            ip_filters.append(f"host {ip}")
        
        # 构建端口过滤器
        port_filters = []
        for port in self.game_ports:
            port_filters.append(f"port {port}")
        
        # 组合过滤器
        filter_parts = []
        if ip_filters:
            filter_parts.append(f"({' or '.join(ip_filters)})")
        if port_filters:
            filter_parts.append(f"({' or '.join(port_filters)})")
        
        return " and ".join(filter_parts) if filter_parts else "tcp"
    
    def _capture_packets(self):
        """数据包捕获主循环"""
        try:
            for packet in self.capture.sniff_continuously():
                if not self.monitoring_active:
                    break
                
                # 分析数据包
                analysis_result = self.analyze_packet(packet)
                
                if analysis_result['is_game_packet']:
                    self._process_game_packet(analysis_result)
                    
        except Exception as e:
            logger.error(f"数据包捕获过程中发生错误: {e}")
        finally:
            logger.info("数据包捕获已停止")
    
    def stop_packet_capture(self):
        """停止数据包捕获"""
        if not self.monitoring_active:
            logger.warning("数据包捕获未在运行")
            return
        
        self.monitoring_active = False
        
        if self.capture:
            try:
                self.capture.close()
            except:
                pass
        
        if self.capture_thread and self.capture_thread.is_alive():
            self.capture_thread.join(timeout=5)
        
        logger.info("数据包捕获已停止")

    def analyze_packet(self, packet):
        """
        分析单个数据包

        Args:
            packet: pyshark数据包对象

        Returns:
            dict: 分析结果
        """
        analysis_result = {
            'timestamp': datetime.now(),
            'is_game_packet': False,
            'packet_type': 'unknown',
            'source_ip': None,
            'dest_ip': None,
            'source_port': None,
            'dest_port': None,
            'data_length': 0,
            'game_data': {},
            'gpi_command': None,
            'hero_info': None,
            'task_info': None
        }

        try:
            # 检查是否为7fgame相关数据包
            if not self._is_7fgame_packet(packet):
                return analysis_result

            # 提取基本信息
            if hasattr(packet, 'ip'):
                analysis_result['source_ip'] = packet.ip.src
                analysis_result['dest_ip'] = packet.ip.dst

            if hasattr(packet, 'tcp'):
                analysis_result['source_port'] = int(packet.tcp.srcport)
                analysis_result['dest_port'] = int(packet.tcp.dstport)
                analysis_result['data_length'] = int(packet.tcp.len) if hasattr(packet.tcp, 'len') else 0

            # 标记为游戏数据包
            analysis_result['is_game_packet'] = True
            self.packet_stats['total_packets'] += 1
            self.packet_stats['game_packets'] += 1

            # 分析数据包内容
            if hasattr(packet, 'tcp') and hasattr(packet.tcp, 'payload'):
                payload = packet.tcp.payload
                analysis_result = self._analyze_payload(payload, analysis_result)

        except Exception as e:
            logger.error(f"分析数据包时发生错误: {e}")

        return analysis_result

    def _is_7fgame_packet(self, packet):
        """
        判断是否为7fgame相关数据包

        Args:
            packet: 数据包对象

        Returns:
            bool: 是否为7fgame数据包
        """
        try:
            # 检查IP地址
            if hasattr(packet, 'ip'):
                src_ip = packet.ip.src
                dst_ip = packet.ip.dst

                # 检查是否与7fgame服务器通信
                for server_ip in self.server_ips:
                    if src_ip == server_ip or dst_ip == server_ip:
                        return True

            # 检查端口
            if hasattr(packet, 'tcp'):
                src_port = int(packet.tcp.srcport)
                dst_port = int(packet.tcp.dstport)

                # 检查是否使用7fgame端口
                for game_port in self.game_ports:
                    if src_port == game_port or dst_port == game_port:
                        return True

            return False

        except Exception as e:
            logger.debug(f"检查7fgame数据包时发生错误: {e}")
            return False

    def _analyze_payload(self, payload, analysis_result):
        """
        分析数据包载荷

        Args:
            payload: 数据包载荷
            analysis_result: 分析结果字典

        Returns:
            dict: 更新后的分析结果
        """
        try:
            # 将载荷转换为字节
            if isinstance(payload, str):
                payload_bytes = bytes.fromhex(payload.replace(':', ''))
            else:
                payload_bytes = bytes(payload)

            # 检查GPI协议命令
            gpi_command = self._extract_gpi_command(payload_bytes)
            if gpi_command:
                analysis_result['gpi_command'] = gpi_command
                analysis_result['packet_type'] = self.gpi_commands.get(gpi_command, f'UNKNOWN_{gpi_command}')
                self.packet_stats['gpi_protocol_packets'] += 1

                # 根据命令类型进行具体分析
                if gpi_command == 318:  # HERO_SELECT
                    hero_info = self._analyze_hero_selection(payload_bytes)
                    if hero_info:
                        analysis_result['hero_info'] = hero_info
                        self.packet_stats['hero_selection_packets'] += 1

                elif gpi_command == 321:  # SKILL_USE
                    skill_info = self._analyze_skill_usage(payload_bytes)
                    if skill_info:
                        analysis_result['skill_info'] = skill_info
                        self.packet_stats['skill_usage_packets'] += 1

                elif gpi_command == 342:  # TASK_UPDATE
                    task_info = self._analyze_task_update(payload_bytes)
                    if task_info:
                        analysis_result['task_info'] = task_info
                        self.packet_stats['task_progress_packets'] += 1

            # 检查协议特征
            for signature_name, signature_bytes in self.protocol_signatures.items():
                if signature_bytes in payload_bytes:
                    analysis_result['packet_type'] = signature_name
                    logger.debug(f"检测到协议特征: {signature_name}")
                    break

        except Exception as e:
            logger.error(f"分析载荷时发生错误: {e}")

        return analysis_result

    def _extract_gpi_command(self, payload_bytes):
        """
        从载荷中提取GPI协议命令

        Args:
            payload_bytes: 载荷字节数据

        Returns:
            int: GPI命令ID，如果未找到则返回None
        """
        try:
            # GPI协议通常在数据包开头包含命令ID
            if len(payload_bytes) >= 4:
                # 尝试不同的字节序
                command_be = struct.unpack('>I', payload_bytes[:4])[0]  # 大端序
                command_le = struct.unpack('<I', payload_bytes[:4])[0]  # 小端序

                # 检查是否为已知命令
                if command_be in self.gpi_commands:
                    return command_be
                elif command_le in self.gpi_commands:
                    return command_le

                # 尝试从偏移位置提取
                if len(payload_bytes) >= 8:
                    command_be_offset = struct.unpack('>I', payload_bytes[4:8])[0]
                    command_le_offset = struct.unpack('<I', payload_bytes[4:8])[0]

                    if command_be_offset in self.gpi_commands:
                        return command_be_offset
                    elif command_le_offset in self.gpi_commands:
                        return command_le_offset

            return None

        except Exception as e:
            logger.debug(f"提取GPI命令时发生错误: {e}")
            return None

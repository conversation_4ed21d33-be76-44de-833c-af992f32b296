﻿<html>
<head>
    <title></title>
</head>
<body style="margin: 0PX; padding: PX; overflow:hidden;" scroll="no">
    <table width="100%" height="100%">
        <tr>
            <td align="center" valign="middle">
                <img id="errorImg" alt="" src="images/7fdefault_104.jpg" />
            </td>
        </tr>
    </table>

    <script type="text/javascript">
        var eImg = document.getElementById("errorImg");
        window.onresize = function() {
            onresizeImg();
        }
        function onresizeImg() {
            if (document.body.offsetWidth >= 350 && document.body.offsetHeight >= 350) {
                eImg.width = "250";
            }
            else {
                if (document.body.offsetWidth < 350) {
                    eImg.width = 250 * (document.body.offsetWidth / 350)
                }
                else if (document.body.offsetHeight < 350) {
                    eImg.width = 250 * (document.body.offsetHeight / 350)
                }
            }

        }
        onresizeImg();
    </script>

</body>
</html>
